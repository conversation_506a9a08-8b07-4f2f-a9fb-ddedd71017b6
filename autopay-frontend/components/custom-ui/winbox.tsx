'use client'

import { cn } from '@/lib/utils/utils'
import dynamic from 'next/dynamic'
import { ReactNode, useEffect, useRef, useState } from 'react'

// Dynamically import WinBox with SSR disabled
const WinBox = dynamic(() => import('react-winbox'), {
  ssr: false,
  loading: () => null,
})

interface WinboxProps {
  isOpen: boolean
  title?: string
  onClose?: () => void
  children?: ReactNode
  options?: {
    className?: string
    toggleEventName?: string
    [key: string]: any
  }
  className?: string
}

export function WinboxComponent({
  isOpen,
  title = 'WinBox',
  onClose,
  children,
  options = {},
  className,
  ...rest
}: WinboxProps) {
  const winboxRef = useRef<any>(null)
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    // Apply WinBox styles
    const style = document.createElement('style')
    style.textContent = `
      .winbox {
        border-radius: var(--radius);
      }
      
      .winbox .wb-body {
        background: hsl(var(--background));
        color: hsl(var(--foreground));
        border-radius: 0 0 var(--radius) var(--radius);
      }
      
      .winbox .wb-title {
        color: hsl(var(--foreground));
      }
      
      .winbox .wb-icon * {
        background: hsl(var(--foreground));
      }
      
      .winbox .wb-body {
        margin: 4px;
        overflow: auto;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  if (!isOpen || !mounted) return null

  const handleClose = () => {
    if (onClose) {
      onClose()
      return false // Prevent closing, let the parent handle it
    }
    if (options.toggleEventName) {
      console.warn('Event emitter not implemented. Please provide onClose prop.')
      return false
    }
    return true
  }

  return (
    <WinBox
      ref={winboxRef}
      title={title}
      className={cn('winbox', options.className, className)}
      onClose={handleClose}
      {...options}
      {...rest}>
      {children}
    </WinBox>
  )
}

// TypeScript types for react-winbox
declare module 'react-winbox' {
  import { ForwardRefExoticComponent, ReactNode } from 'react'

  export interface WinBoxProps {
    title?: string
    className?: string
    onClose?: () => boolean | void | undefined
    children?: ReactNode
    [key: string]: any
  }

  const WinBox: ForwardRefExoticComponent<WinBoxProps>
  export default WinBox
}
