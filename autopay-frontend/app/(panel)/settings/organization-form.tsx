'use client'

import { zodResolver } from '@hookform/resolvers/zod'
import { useForm } from 'react-hook-form'
import { z } from 'zod'

import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import { Input } from '@/components/ui/input'
import { <PERSON><PERSON>, <PERSON>bs<PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Textarea } from '@/components/ui/textarea'
import { toast } from '@/components/ui/use-toast'
import { useAuth } from '@/lib/hooks/useAuth'
import { fetchHelper, queryFetchHelper } from '@/lib/utils/fetchHelper'
import { useQuery } from '@tanstack/react-query'
import { CreditCard, Globe, Paintbrush } from 'lucide-react'
import { useState } from 'react'
import { FaBuilding } from 'react-icons/fa6'
import { MdOutlineSettingsApplications } from 'react-icons/md'
import DomainsPage from './domains/page'

const organizationFormSchema = z.object({
  domainConfig: z.object({
    data: z
      .object({
        branding: z
          .object({
            name: z.string().optional(),
            slogan: z.string().optional(),
            logo_url: z.string().url().optional().or(z.literal('')),
            favicon_url: z.string().url().optional().or(z.literal('')),
          })
          .optional(),
        theme: z
          .object({
            colors: z.object({
              primary: z.string(),
              secondary: z.string(),
              accent: z.string(),
              background: z.string(),
              surface: z.string(),
              text: z.string(),
              text_secondary: z.string(),
            }),
            custom_css: z.record(z.any()).optional(),
          })
          .optional(),
        seo: z
          .object({
            title: z.string().optional(),
            description: z.string().optional(),
            keywords: z.string().optional(),
            og_image: z.string().url().optional().or(z.literal('')),
          })
          .optional(),
        contact: z.record(z.any()).optional(),
        custom: z.record(z.any()).optional(),
      })
      .optional(),
  }),
  paymentInfo: z.object({
    bank_name: z.string().optional(),
    account_number: z.string().optional(),
    account_holder: z.string().optional(),
    branch: z.string().optional(),
    swift_code: z.string().optional(),
    tax_code: z.string().optional(),
    company_address: z.string().optional(),
  }),
})

type OrganizationFormValues = z.infer<typeof organizationFormSchema>

const defaultValues: Partial<OrganizationFormValues> = {
  domainConfig: {
    data: {
      branding: {
        name: '',
        slogan: '',
        logo_url: '',
        favicon_url: '',
      },
      theme: {
        colors: {
          primary: '#3b82f6',
          secondary: '#64748b',
          accent: '#f59e0b',
          background: '#ffffff',
          surface: '#f8fafc',
          text: '#1e293b',
          text_secondary: '#64748b',
        },
        custom_css: {},
      },
      seo: {
        title: '',
        description: '',
        keywords: '',
        og_image: '',
      },
      contact: {},
      custom: {},
    },
  },
  paymentInfo: {
    bank_name: '',
    account_number: '',
    account_holder: '',
    branch: '',
    swift_code: '',
    tax_code: '',
    company_address: '',
  },
}

interface Domain {
  id: string
  hostname: string
  name: string
  description?: string
  domain_type: 'subdomain' | 'custom'
  status: 'pending' | 'active' | 'failed' | 'suspended'
  is_active: boolean
  created_at: string
}

export function OrganizationForm() {
  const { user } = useAuth()
  const [activeTab, setActiveTab] = useState('branding')
  const [is404Error, setIs404Error] = useState(false)

  console.log('🏢 OrganizationForm render - activeTab:', activeTab, 'user orgId:', user?.current_organization?.id)

  const form = useForm<OrganizationFormValues>({
    resolver: zodResolver(organizationFormSchema),
    defaultValues,
    mode: 'onChange',
  })

  // Fetch domain information to check if domain exists and is configured
  const { data: domain = null, isLoading: isDomainLoading } = useQuery<Domain | null>({
    queryKey: ['domains', user?.current_organization?.id],
    queryFn: async (): Promise<Domain | null> => {
      const orgId = user?.current_organization?.id
      if (!orgId) return null

      try {
        const response = await queryFetchHelper(`/${orgId}/domains`)
        setIs404Error(false)
        return response.data || null
      } catch (error: any) {
        if (error?.code === 404) {
          setIs404Error(true)
          return null
        }
        setIs404Error(false)
        throw error
      }
    },
    enabled: !!user?.current_organization?.id,
  })

  // Determine if update button should be shown
  const shouldShowUpdateButton = () => {
    // Always show button for non-domains tabs
    if (activeTab !== 'domains') {
      return true
    }

    // For domains tab, only show if domain exists and is configured
    return domain && !is404Error && !isDomainLoading
  }

  async function onSubmit(data: OrganizationFormValues) {
    console.log('🔥 OrganizationForm onSubmit called with data:', data)

    // TEMPORARILY DISABLED - This was causing unwanted PUT requests
    console.log('⚠️ OrganizationForm submit temporarily disabled to prevent unwanted API calls')
    toast({
      title: 'Thông báo',
      description: 'Chức năng cập nhật cấu hình tổ chức đang được phát triển',
    })
    return

    // try {
    //   // For now, we'll just update the domain config
    //   // In a real implementation, you'd get the domain ID from context or props
    //   const response = await fetchHelper('/domains/1', {
    //     method: 'PUT',
    //     body: JSON.stringify(data.domainConfig),
    //   })

    //   if (response.success) {
    //     toast({
    //       title: 'Thành công',
    //       description: 'Cấu hình tổ chức đã được cập nhật',
    //     })
    //   } else {
    //     toast({
    //       title: 'Lỗi',
    //       description: response.message || 'Có lỗi xảy ra khi cập nhật cấu hình',
    //       variant: 'destructive',
    //     })
    //   }
    // } catch (error) {
    //   toast({
    //     title: 'Lỗi',
    //     description: 'Có lỗi xảy ra khi cập nhật cấu hình',
    //     variant: 'destructive',
    //   })
    // }
  }

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-6">
        <div className="space-y-6">
          <div className="space-y-2">
            <h2 className="text-lg font-semibold">Cấu hình tổ chức</h2>
            <p className="text-muted-foreground text-sm">
              Quản lý thông tin thương hiệu và giao diện cho tổ chức của bạn
            </p>
          </div>

          <Tabs
            defaultValue="branding"
            value={activeTab}
            onValueChange={setActiveTab}
            className="w-full">
            <TabsList className="grid w-full grid-cols-5">
              <TabsTrigger value="branding">
                <FaBuilding className="mr-2 h-4 w-4" />
                Tổ chức
              </TabsTrigger>
              <TabsTrigger value="domains">
                <Globe className="mr-2 h-4 w-4" />
                Domains
              </TabsTrigger>
              <TabsTrigger value="payment">
                <CreditCard className="mr-2 h-4 w-4" />
                Thanh toán
              </TabsTrigger>
              <TabsTrigger value="theme">
                <Paintbrush className="mr-2 h-4 w-4" />
                Giao diện
              </TabsTrigger>
              <TabsTrigger value="advanced">
                <MdOutlineSettingsApplications className="mr-2 h-4 w-4" />
                Thiết lập thêm
              </TabsTrigger>
            </TabsList>

            <TabsContent
              value="branding"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Thông tin cơ bản</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="domainConfig.data.branding.name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tên thương hiệu</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="AutoPAY"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.data.branding.slogan"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Slogan</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Thanh toán tự động thông minh"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="domainConfig.data.branding.logo_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL Logo</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://example.com/logo.png"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="domainConfig.data.branding.favicon_url"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>URL Favicon</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://example.com/favicon.ico"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="theme"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Cấu hình màu sắc</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    {['primary', 'secondary', 'accent', 'background', 'surface', 'text', 'text_secondary'].map(
                      (colorKey) => (
                        <FormField
                          key={colorKey}
                          control={form.control}
                          name={`domainConfig.data.theme.colors.${colorKey}` as any}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="capitalize">{colorKey.replace('_', ' ')}</FormLabel>
                              <div className="flex gap-2">
                                <FormControl>
                                  <Input
                                    type="color"
                                    {...field}
                                    className="h-10 w-16"
                                  />
                                </FormControl>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder="#000000"
                                    className="flex-1"
                                  />
                                </FormControl>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )
                    )}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="domains"
              className="space-y-4">
              <DomainsPage />
            </TabsContent>

            <TabsContent
              value="payment"
              className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Thông tin thanh toán</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.bank_name"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tên ngân hàng</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Vietcombank"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.account_number"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Số tài khoản</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="**********"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.account_holder"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chủ tài khoản</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="CÔNG TY TNHH ABC"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.branch"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Chi nhánh</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="Chi nhánh Hà Nội"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="paymentInfo.swift_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mã SWIFT</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="BFTVVNVX"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="paymentInfo.tax_code"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Mã số thuế</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="0123456789"
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>

                  <FormField
                    control={form.control}
                    name="paymentInfo.company_address"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Địa chỉ công ty</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Số 123, Đường ABC, Phường XYZ, Quận DEF, Thành phố Hà Nội"
                            rows={3}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent
              value="advanced"
              className="space-y-4">
              {/* SEO Settings */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">SEO</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <FormField
                    control={form.control}
                    name="domainConfig.data.seo.title"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Title</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="AutoPAY - Thanh toán tự động"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.data.seo.description"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Description</FormLabel>
                        <FormControl>
                          <Textarea
                            {...field}
                            placeholder="Mô tả cho SEO..."
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.data.seo.keywords"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Meta Keywords</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="thanh toán, tự động, autopay"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="domainConfig.data.seo.og_image"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Open Graph Image URL</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://example.com/og-image.jpg"
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>

          {shouldShowUpdateButton() && (
            <Button
              type="submit"
              size="sm">
              Cập nhật cấu hình
            </Button>
          )}
        </div>
      </form>
    </Form>
  )
}
